# 音乐流派分类期末作业项目

基于GTZAN数据集的音乐流派分类研究 - 非结构化数据挖掘期末作业

## 项目概述

本项目实现了基于GTZAN音乐数据集的音乐流派自动分类系统，使用K近邻（KNN）算法和深度神经网络两种机器学习方法，对10种音乐流派进行分类识别。

### 主要功能

- 🎵 **音频特征分析**: 提取和分析57维音频特征
- 📊 **数据可视化**: 生成数据分布、特征相关性等可视化图表
- 🤖 **机器学习模型**: 实现KNN和神经网络两种分类算法
- 📈 **性能评估**: 详细的模型评估和性能对比分析
- 📝 **自动报告**: 生成完整的实验报告和论文

### 支持的音乐流派

- Blues (蓝调)
- Classical (古典)
- Country (乡村)
- Disco (迪斯科)
- Hiphop (嘻哈)
- Jazz (爵士)
- Metal (金属)
- Pop (流行)
- Reggae (雷鬼)
- Rock (摇滚)

## 项目结构

```
├── music-genre-classifier-main/     # 主要代码目录
│   ├── music_genre_classifier/      # 核心模块
│   │   ├── dataset/                 # 数据处理模块
│   │   ├── models/                  # 模型实现
│   │   └── __main__.py             # 主程序入口
│   ├── configs/                     # 配置文件
│   ├── data_analysis.py            # 数据分析脚本
│   └── model_evaluation.py         # 模型评估脚本
├── data/                           # 数据目录
│   └── Data/
│       ├── features_30_sec.csv     # 30秒音频特征
│       └── features_3_sec.csv      # 3秒音频特征
├── results/                        # 结果输出目录
├── run_experiment.py               # 自动化实验脚本
├── 基于GTZAN数据集的音乐流派分类研究.md  # 完整论文
└── README.md                       # 项目说明
```

## 快速开始

### 1. 环境要求

- Python 3.9+
- 必需的Python包：
  ```
  numpy>=1.21.0
  pandas>=1.3.4
  matplotlib>=3.5.0
  seaborn>=0.11.0
  scikit-learn>=1.0.1
  tensorflow>=2.7.0
  keras-tuner>=1.1.0
  pyyaml>=6.0
  ```

### 2. 安装依赖

```bash
pip install numpy pandas matplotlib seaborn scikit-learn tensorflow keras-tuner pyyaml
```

### 3. 运行实验

#### 方法一：自动化脚本（推荐）

```bash
python run_experiment.py
```

脚本提供三种模式：
1. **完整实验**: 包含完整的模型训练和评估（耗时较长）
2. **快速实验**: 仅运行KNN模型和数据分析（适合测试）
3. **仅数据分析**: 只生成数据可视化图表

#### 方法二：手动运行

```bash
# 进入项目目录
cd music-genre-classifier-main

# 运行数据分析
python data_analysis.py

# 运行模型训练和评估
python model_evaluation.py

# 或者运行主程序
python -m music_genre_classifier configs/default.yaml --save_plots --display_results
```

## 生成的结果文件

### 数据分析结果
- `results/basic_statistics.txt` - 数据集基本统计信息
- `results/genre_distribution.png` - 音乐流派分布图
- `results/feature_distributions.png` - 主要特征分布图
- `results/feature_correlation.png` - 特征相关性热力图
- `results/dimensionality_reduction.png` - PCA和t-SNE降维可视化
- `results/pca_analysis.txt` - PCA分析结果

### 模型评估结果
- `results/model_results.txt` - 模型训练结果摘要
- `results/KNN_evaluation.txt` - KNN模型详细评估
- `results/NeuralNet_evaluation.txt` - 神经网络模型详细评估
- `results/KNN_confusion_matrix.png` - KNN混淆矩阵
- `results/NeuralNet_confusion_matrix.png` - 神经网络混淆矩阵
- `results/model_comparison.png` - 模型性能对比图
- `results/genre_performance_analysis.png` - 各流派性能分析
- `results/comprehensive_evaluation_report.txt` - 综合评估报告

### 论文文档
- `基于GTZAN数据集的音乐流派分类研究.md` - 完整的期末作业论文

## 论文使用说明

生成的论文文档 `基于GTZAN数据集的音乐流派分类研究.md` 已按照作业模板格式化，包含：

1. **完整的章节结构**: 按照期末作业要求组织
2. **图片插入位置标注**: 所有需要插入图片的位置都已标注
3. **代码示例**: 包含关键的代码实现
4. **实验结果占位符**: 标注了需要填入实际实验结果的位置

### 使用步骤：

1. 运行实验生成所有结果文件
2. 将生成的图片插入到论文中标注的位置
3. 根据 `results/` 目录中的结果文件填入具体数值
4. 填写个人信息（姓名、学号等）
5. 根据实际结果调整分析和结论

## 配置说明

### 主要配置文件

- `configs/default.yaml`: 完整实验配置（KNN + 神经网络）
- `configs/knn_only.yaml`: 仅KNN模型配置
- `configs/neural_net_only.yaml`: 仅神经网络配置
- `configs/quick_test.yaml`: 快速测试配置

### 自定义配置

可以修改配置文件来调整：
- 使用的音频特征
- 模型超参数
- 训练轮数
- 数据集路径

## 常见问题

### Q: 运行时提示找不到数据文件？
A: 确保 `data/Data/` 目录下有 `features_30_sec.csv` 和 `features_3_sec.csv` 文件。

### Q: 训练时间太长怎么办？
A: 可以使用快速实验模式或仅运行KNN模型：
```bash
python -m music_genre_classifier configs/knn_only.yaml --display_results
```

### Q: 如何只生成数据分析图表？
A: 运行数据分析脚本：
```bash
cd music-genre-classifier-main
python data_analysis.py
```

### Q: 内存不足怎么办？
A: 可以修改配置文件，减少使用的特征数量或使用较小的神经网络。

## 技术特点

- **模块化设计**: 清晰的代码结构，易于理解和修改
- **自动化流程**: 一键运行完整实验
- **丰富的可视化**: 多种图表展示数据和结果
- **详细的评估**: 包含准确率、精确率、召回率、F1分数等多种指标
- **中文支持**: 所有输出和图表都支持中文显示

## 贡献

本项目基于开源项目 [music-genre-classifier](https://github.com/ryansingman/music-genre-classifier) 进行了大幅改进和扩展，增加了：

- 完整的中文数据分析和可视化
- 详细的模型评估和对比
- 自动化实验流程
- 符合课程要求的论文模板
- 丰富的结果展示和分析

## 许可证

MIT License

## 联系方式

如有问题，请通过以下方式联系：
- 提交 Issue
- 发送邮件

---

**祝您期末作业顺利完成！** 🎓
