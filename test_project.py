#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目测试脚本
用于验证项目环境和基本功能是否正常
"""

import os
import sys
import numpy as np
import pandas as pd

def test_data_files():
    """测试数据文件是否存在"""
    print("测试数据文件...")
    
    data_files = [
        'data/Data/features_30_sec.csv',
        'data/Data/features_3_sec.csv'
    ]
    
    for file_path in data_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} 存在")
            
            # 读取文件头部检查格式
            try:
                df = pd.read_csv(file_path, nrows=5)
                print(f"  - 形状: {df.shape}")
                print(f"  - 列数: {len(df.columns)}")
            except Exception as e:
                print(f"  - 读取错误: {e}")
        else:
            print(f"✗ {file_path} 不存在")
            return False
    
    return True

def test_imports():
    """测试必要的包导入"""
    print("\n测试包导入...")
    
    packages = [
        ('numpy', 'np'),
        ('pandas', 'pd'),
        ('matplotlib.pyplot', 'plt'),
        ('seaborn', 'sns'),
        ('sklearn', None),
        ('yaml', None)
    ]
    
    for package, alias in packages:
        try:
            if alias:
                exec(f"import {package} as {alias}")
            else:
                exec(f"import {package}")
            print(f"✓ {package}")
        except ImportError as e:
            print(f"✗ {package}: {e}")
            return False
    
    return True

def test_project_structure():
    """测试项目结构"""
    print("\n测试项目结构...")
    
    required_files = [
        'music-genre-classifier-main/music_genre_classifier/__main__.py',
        'music-genre-classifier-main/music_genre_classifier/__init__.py',
        'music-genre-classifier-main/configs/default.yaml',
        'music-genre-classifier-main/data_analysis.py',
        'music-genre-classifier-main/model_evaluation.py'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} 缺失")
            return False
    
    return True

def test_data_loading():
    """测试数据加载功能"""
    print("\n测试数据加载...")

    try:
        # 保存当前目录
        original_dir = os.getcwd()

        # 切换到项目目录
        os.chdir('music-genre-classifier-main')

        # 添加项目路径
        sys.path.append('.')

        from music_genre_classifier import dataset
        import yaml

        # 读取配置
        with open('configs/default.yaml', 'r') as f:
            config = yaml.load(f, Loader=yaml.Loader)

        # 尝试加载数据
        print("  正在加载数据集...")
        full_ds = dataset.create_gtzan_dataset(**config["dataset"])

        print(f"✓ 数据加载成功")
        print(f"  - 数据形状: {full_ds.shape}")
        print(f"  - 样本数: {full_ds.shape[0]}")
        print(f"  - 特征数: {full_ds.shape[1] - 1}")

        # 检查数据类型
        _, labels = dataset.split_features_and_labels(full_ds)
        unique_labels = np.unique(labels)
        print(f"  - 类别数: {len(unique_labels)}")
        print(f"  - 类别标签: {unique_labels}")

        # 恢复原目录
        os.chdir(original_dir)

        return True

    except Exception as e:
        print(f"✗ 数据加载失败: {e}")
        # 确保恢复原目录
        try:
            os.chdir(original_dir)
        except:
            pass
        return False

def test_basic_visualization():
    """测试基本可视化功能"""
    print("\n测试基本可视化...")
    
    try:
        import matplotlib.pyplot as plt

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建简单测试图
        _, ax = plt.subplots(figsize=(6, 4))
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        ax.plot(x, y)
        ax.set_title('测试图表')
        
        # 保存测试图
        if not os.path.exists('test_results'):
            os.makedirs('test_results')
        
        plt.savefig('test_results/test_plot.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✓ 可视化功能正常")
        print("  - 测试图表已保存到 test_results/test_plot.png")
        
        return True
        
    except Exception as e:
        print(f"✗ 可视化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("音乐流派分类项目测试")
    print("=" * 50)
    
    tests = [
        ("数据文件检查", test_data_files),
        ("包导入检查", test_imports),
        ("项目结构检查", test_project_structure),
        ("数据加载测试", test_data_loading),
        ("可视化测试", test_basic_visualization)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！项目可以正常运行。")
        print("\n下一步:")
        print("1. 运行 'python run_experiment.py' 开始完整实验")
        print("2. 或运行 'python music-genre-classifier-main/data_analysis.py' 仅生成数据分析")
    else:
        print("❌ 部分测试失败，请检查环境配置。")
        print("\n建议:")
        print("1. 确保所有依赖包已安装")
        print("2. 检查数据文件是否在正确位置")
        print("3. 确保项目文件完整")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
