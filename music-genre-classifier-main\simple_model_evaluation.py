#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的音乐流派分类模型评估脚本
专注于KNN模型和基本的神经网络，避免复杂的超参数调优
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.metrics import precision_recall_fscore_support
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import GridSearchCV
import tensorflow as tf
from tensorflow import keras
import yaml

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 导入项目模块
import sys
sys.path.append('.')
from music_genre_classifier import dataset


def load_data():
    """加载数据"""
    with open('configs/default.yaml', 'r') as f:
        config = yaml.load(f, Loader=yaml.Loader)
    
    full_ds = dataset.create_gtzan_dataset(**config["dataset"])
    train_ds, test_ds = dataset.split_dataset(full_ds)
    
    return train_ds, test_ds, config


def train_knn_model(train_ds, test_ds):
    """训练KNN模型"""
    print("训练KNN模型...")
    
    # 获取特征和标签
    train_features, train_labels = dataset.split_features_and_labels(train_ds)
    test_features, test_labels = dataset.split_features_and_labels(test_ds)
    
    # 标准化特征
    scaler = StandardScaler()
    train_features_scaled = scaler.fit_transform(train_features)
    test_features_scaled = scaler.transform(test_features)
    
    # 简化的超参数搜索
    param_grid = {
        'n_neighbors': [1, 3, 5, 7],
        'weights': ['uniform', 'distance'],
        'metric': ['euclidean', 'manhattan']
    }
    
    knn = KNeighborsClassifier()
    grid_search = GridSearchCV(knn, param_grid, cv=3, scoring='accuracy', n_jobs=-1)
    grid_search.fit(train_features_scaled, train_labels)
    
    # 最佳模型
    best_knn = grid_search.best_estimator_
    
    # 预测
    predictions = best_knn.predict(test_features_scaled)
    
    return {
        'model': best_knn,
        'predictions': predictions,
        'true_labels': test_labels,
        'best_params': grid_search.best_params_,
        'scaler': scaler
    }


def train_simple_neural_network(train_ds, test_ds):
    """训练简单的神经网络"""
    print("训练神经网络模型...")
    
    # 获取特征和标签
    train_features, train_labels = dataset.split_features_and_labels(train_ds)
    test_features, test_labels = dataset.split_features_and_labels(test_ds)
    
    # 标准化特征
    scaler = StandardScaler()
    train_features_scaled = scaler.fit_transform(train_features)
    test_features_scaled = scaler.transform(test_features)
    
    # 构建简单的神经网络
    model = keras.Sequential([
        keras.layers.Dense(128, activation='relu', input_shape=(train_features.shape[1],)),
        keras.layers.Dropout(0.3),
        keras.layers.Dense(64, activation='relu'),
        keras.layers.Dropout(0.3),
        keras.layers.Dense(10, activation='softmax')
    ])
    
    model.compile(
        optimizer='adam',
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    # 训练模型
    early_stopping = keras.callbacks.EarlyStopping(
        monitor='val_loss', 
        patience=10, 
        restore_best_weights=True
    )
    
    history = model.fit(
        train_features_scaled, train_labels,
        epochs=50,
        validation_split=0.2,
        callbacks=[early_stopping],
        verbose=1
    )
    
    # 预测
    predictions_prob = model.predict(test_features_scaled)
    predictions = np.argmax(predictions_prob, axis=1)
    
    return {
        'model': model,
        'predictions': predictions,
        'true_labels': test_labels,
        'history': history,
        'scaler': scaler
    }


def evaluate_model(results, model_name, save_dir="results"):
    """评估模型性能"""
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    genre_names = ['blues', 'classical', 'country', 'disco', 'hiphop', 
                   'jazz', 'metal', 'pop', 'reggae', 'rock']
    
    predictions = results['predictions']
    true_labels = results['true_labels']
    
    # 计算评估指标
    accuracy = accuracy_score(true_labels, predictions)
    precision, recall, f1, support = precision_recall_fscore_support(
        true_labels, predictions, average=None, labels=range(10)
    )
    
    # 计算平均指标
    avg_precision = np.mean(precision)
    avg_recall = np.mean(recall)
    avg_f1 = np.mean(f1)
    
    # 生成混淆矩阵
    cm = confusion_matrix(true_labels, predictions)
    
    # 保存详细结果
    with open(f'{save_dir}/{model_name}_evaluation.txt', 'w', encoding='utf-8') as f:
        f.write(f"{model_name} 模型评估结果\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"总体准确率: {accuracy:.4f}\n")
        f.write(f"平均精确率: {avg_precision:.4f}\n")
        f.write(f"平均召回率: {avg_recall:.4f}\n")
        f.write(f"平均F1分数: {avg_f1:.4f}\n\n")
        
        if 'best_params' in results:
            f.write(f"最佳超参数: {results['best_params']}\n\n")
        
        f.write("各类别详细指标:\n")
        f.write("-" * 30 + "\n")
        for i, genre in enumerate(genre_names):
            f.write(f"{genre:>10}: 精确率={precision[i]:.4f}, "
                   f"召回率={recall[i]:.4f}, F1={f1[i]:.4f}, "
                   f"支持数={support[i]}\n")
    
    # 创建混淆矩阵图
    plt.figure(figsize=(10, 8))
    cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100
    
    sns.heatmap(cm_percent, annot=True, fmt='.1f', cmap='Blues',
                xticklabels=genre_names, yticklabels=genre_names,
                cbar_kws={'label': '百分比 (%)'})
    
    plt.title(f'{model_name} 混淆矩阵 (百分比)', fontsize=16, fontweight='bold')
    plt.xlabel('预测标签', fontsize=12)
    plt.ylabel('真实标签', fontsize=12)
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig(f'{save_dir}/{model_name}_confusion_matrix.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'support': support,
        'confusion_matrix': cm
    }


def create_comparison_plot(knn_results, nn_results, save_dir="results"):
    """创建模型对比图"""
    models = ['KNN', '神经网络']
    metrics = ['准确率', '精确率', '召回率', 'F1分数']
    
    data = {
        'KNN': [
            knn_results['accuracy'],
            np.mean(knn_results['precision']),
            np.mean(knn_results['recall']),
            np.mean(knn_results['f1'])
        ],
        '神经网络': [
            nn_results['accuracy'],
            np.mean(nn_results['precision']),
            np.mean(nn_results['recall']),
            np.mean(nn_results['f1'])
        ]
    }
    
    x = np.arange(len(metrics))
    width = 0.35
    
    fig, ax = plt.subplots(figsize=(12, 6))
    
    bars1 = ax.bar(x - width/2, data['KNN'], width, label='KNN', color='lightblue', alpha=0.8)
    bars2 = ax.bar(x + width/2, data['神经网络'], width, label='神经网络', color='lightcoral', alpha=0.8)
    
    ax.set_xlabel('评估指标', fontsize=12)
    ax.set_ylabel('分数', fontsize=12)
    ax.set_title('模型性能对比', fontsize=16, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(metrics)
    ax.legend()
    ax.grid(axis='y', alpha=0.3)
    ax.set_ylim(0, 1)
    
    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{height:.3f}', ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/model_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()


def main():
    """主函数"""
    print("开始简化模型评估...")
    
    # 加载数据
    train_ds, test_ds, config = load_data()
    print(f"数据加载完成")
    
    # 创建结果目录
    save_dir = "results"
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # 训练KNN模型
    knn_model_results = train_knn_model(train_ds, test_ds)
    knn_eval_results = evaluate_model(knn_model_results, "KNN", save_dir)
    
    print(f"KNN模型完成 - 准确率: {knn_eval_results['accuracy']:.4f}")
    
    # 训练神经网络模型
    try:
        nn_model_results = train_simple_neural_network(train_ds, test_ds)
        nn_eval_results = evaluate_model(nn_model_results, "NeuralNet", save_dir)
        print(f"神经网络模型完成 - 准确率: {nn_eval_results['accuracy']:.4f}")
        
        # 创建对比图
        create_comparison_plot(knn_eval_results, nn_eval_results, save_dir)
        
    except Exception as e:
        print(f"神经网络训练失败: {e}")
        print("仅使用KNN模型结果")
        nn_eval_results = None
    
    # 保存综合报告
    with open(f'{save_dir}/simple_evaluation_report.txt', 'w', encoding='utf-8') as f:
        f.write("简化模型评估报告\n")
        f.write("=" * 40 + "\n\n")
        
        f.write("KNN模型结果:\n")
        f.write(f"  准确率: {knn_eval_results['accuracy']:.4f}\n")
        f.write(f"  最佳参数: {knn_model_results['best_params']}\n\n")
        
        if nn_eval_results:
            f.write("神经网络模型结果:\n")
            f.write(f"  准确率: {nn_eval_results['accuracy']:.4f}\n")
    
    print(f"\n评估完成！结果保存在 {save_dir} 目录")


if __name__ == "__main__":
    main()
