#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音乐流派分类完整实验运行脚本
用于执行数据分析、模型训练和评估的完整流程
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def print_section(title):
    """打印章节标题"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def run_command(command, description):
    """运行命令并显示进度"""
    print(f"\n正在执行: {description}")
    print(f"命令: {command}")
    print("-" * 40)
    
    start_time = time.time()
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✓ 成功完成 ({end_time - start_time:.1f}秒)")
            if result.stdout:
                print("输出:")
                print(result.stdout)
        else:
            print(f"✗ 执行失败 ({end_time - start_time:.1f}秒)")
            print("错误信息:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"✗ 执行异常: {str(e)}")
        return False
    
    return True

def check_dependencies():
    """检查依赖包"""
    print_section("检查依赖包")
    
    required_packages = [
        ('numpy', 'numpy'),
        ('pandas', 'pandas'),
        ('matplotlib', 'matplotlib'),
        ('seaborn', 'seaborn'),
        ('scikit-learn', 'sklearn'),
        ('tensorflow', 'tensorflow'),
        ('keras-tuner', 'keras_tuner'),
        ('pyyaml', 'yaml')
    ]

    missing_packages = []

    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✓ {package_name}")
        except ImportError:
            print(f"✗ {package_name} (缺失)")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n缺失的包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("\n所有依赖包已安装完成!")
    return True

def setup_environment():
    """设置环境"""
    print_section("设置实验环境")
    
    # 创建必要的目录
    directories = ['results', 'trained_models']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✓ 创建目录: {directory}")
        else:
            print(f"✓ 目录已存在: {directory}")
    
    # 检查数据文件
    data_files = [
        'data/Data/features_30_sec.csv',
        'data/Data/features_3_sec.csv'
    ]
    
    for data_file in data_files:
        if os.path.exists(data_file):
            print(f"✓ 数据文件存在: {data_file}")
        else:
            print(f"✗ 数据文件缺失: {data_file}")
            return False
    
    return True

def run_data_analysis():
    """运行数据分析"""
    print_section("数据分析")
    
    # 切换到项目目录
    os.chdir('music-genre-classifier-main')
    
    # 运行数据分析脚本
    success = run_command(
        'python data_analysis.py',
        '执行数据分析和可视化'
    )
    
    # 返回上级目录
    os.chdir('..')
    
    return success

def run_model_training():
    """运行模型训练和评估"""
    print_section("模型训练和评估")
    
    # 切换到项目目录
    os.chdir('music-genre-classifier-main')
    
    # 运行模型评估脚本
    success = run_command(
        'python model_evaluation.py',
        '执行模型训练和评估'
    )
    
    # 返回上级目录
    os.chdir('..')
    
    return success

def run_quick_experiment():
    """运行快速实验（仅生成图表）"""
    print_section("快速实验模式")
    
    # 切换到项目目录
    os.chdir('music-genre-classifier-main')
    
    # 只运行数据分析生成图表
    success = run_command(
        'python -m music_genre_classifier configs/knn_only.yaml --save_plots --display_results',
        '运行快速实验（KNN模型 + 数据可视化）'
    )
    
    # 返回上级目录
    os.chdir('..')
    
    return success

def generate_summary_report():
    """生成总结报告"""
    print_section("生成实验总结报告")
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    report_content = f"""
# 音乐流派分类实验总结报告

**实验时间**: {timestamp}

## 实验概述

本次实验基于GTZAN音乐数据集，使用机器学习方法进行音乐流派分类研究。

## 生成的文件

### 数据分析结果
- `results/basic_statistics.txt` - 数据集基本统计信息
- `results/genre_distribution.png` - 音乐流派分布图
- `results/feature_distributions.png` - 主要特征分布图
- `results/feature_correlation.png` - 特征相关性热力图
- `results/dimensionality_reduction.png` - 降维可视化图
- `results/pca_analysis.txt` - PCA分析结果

### 模型评估结果
- `results/model_results.txt` - 模型训练结果
- `results/*_evaluation.txt` - 各模型详细评估报告
- `results/*_confusion_matrix.png` - 混淆矩阵图
- `results/model_comparison.png` - 模型性能对比图
- `results/genre_performance_analysis.png` - 各流派性能分析图
- `results/comprehensive_evaluation_report.txt` - 综合评估报告

## 论文文档
- `基于GTZAN数据集的音乐流派分类研究.md` - 完整论文

## 使用说明

1. 所有图表文件可直接插入到论文中对应位置
2. 评估报告包含详细的数值结果，可用于填充论文中的表格
3. 论文模板已按照作业要求格式化，只需填入个人信息和实验结果

## 注意事项

- 图片插入位置已在论文中标注
- 实验结果数值需要根据实际运行结果填入
- 建议根据实际结果调整论文中的分析和结论

---
实验完成时间: {timestamp}
"""
    
    with open('实验总结报告.md', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("✓ 实验总结报告已生成: 实验总结报告.md")

def main():
    """主函数"""
    print("音乐流派分类实验自动化脚本")
    print("=" * 60)
    print("本脚本将自动执行完整的实验流程，包括:")
    print("1. 环境检查和设置")
    print("2. 数据分析和可视化")
    print("3. 模型训练和评估")
    print("4. 结果整理和报告生成")
    print("=" * 60)
    
    # 询问用户选择实验模式
    print("\n请选择实验模式:")
    print("1. 完整实验 (包含模型训练，耗时较长)")
    print("2. 快速实验 (仅数据分析和简单模型，适合测试)")
    print("3. 仅数据分析 (生成图表和统计信息)")
    
    while True:
        choice = input("\n请输入选择 (1/2/3): ").strip()
        if choice in ['1', '2', '3']:
            break
        print("无效选择，请输入 1、2 或 3")
    
    start_time = time.time()
    
    # 1. 检查依赖
    if not check_dependencies():
        print("\n实验终止：依赖包检查失败")
        return
    
    # 2. 设置环境
    if not setup_environment():
        print("\n实验终止：环境设置失败")
        return
    
    # 3. 根据选择执行实验
    if choice == '1':
        # 完整实验
        print("\n开始执行完整实验...")
        
        if not run_data_analysis():
            print("\n实验终止：数据分析失败")
            return
        
        if not run_model_training():
            print("\n实验终止：模型训练失败")
            return
            
    elif choice == '2':
        # 快速实验
        print("\n开始执行快速实验...")
        
        if not run_quick_experiment():
            print("\n实验终止：快速实验失败")
            return
            
    elif choice == '3':
        # 仅数据分析
        print("\n开始执行数据分析...")
        
        if not run_data_analysis():
            print("\n实验终止：数据分析失败")
            return
    
    # 4. 生成总结报告
    generate_summary_report()
    
    # 计算总耗时
    total_time = time.time() - start_time
    
    print_section("实验完成")
    print(f"总耗时: {total_time:.1f} 秒")
    print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n实验结果文件:")
    print("- 论文文档: 基于GTZAN数据集的音乐流派分类研究.md")
    print("- 实验结果: results/ 目录")
    print("- 总结报告: 实验总结报告.md")
    print("\n请查看生成的文件并根据实际结果完善论文内容。")

if __name__ == "__main__":
    main()
