#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音乐流派分类模型评估脚本
用于生成详细的模型评估结果和可视化
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.metrics import precision_recall_fscore_support
import yaml
import pickle

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 导入项目模块
import sys
sys.path.append('music_genre_classifier')
from music_genre_classifier import dataset, models


def load_data_and_models():
    """加载数据和训练模型"""
    # 读取配置文件
    with open('configs/default.yaml', 'r') as f:
        config = yaml.load(f, Loader=yaml.Loader)
    
    # 加载数据集
    full_ds = dataset.create_gtzan_dataset(**config["dataset"])
    train_ds, test_ds = dataset.split_dataset(full_ds)
    
    # 创建模型
    model_trainables = [
        models.build_from_config(model_conf, train_ds, test_ds)
        for model_conf in config["models"]
    ]
    
    return train_ds, test_ds, model_trainables, config


def evaluate_model_performance(model, test_ds, model_name, save_dir="results"):
    """评估单个模型性能"""
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    genre_names = ['blues', 'classical', 'country', 'disco', 'hiphop', 
                   'jazz', 'metal', 'pop', 'reggae', 'rock']
    
    # 获取测试数据
    test_features, test_labels = dataset.split_features_and_labels(test_ds)
    
    # 根据模型类型进行预测
    if hasattr(model, 'normalized_dataset'):  # KNN模型
        scaled_test_features, scaled_test_labels = model.normalized_dataset(test_ds)
        predictions = model._trained_model.predict(scaled_test_features)
        true_labels = scaled_test_labels
    else:  # 神经网络模型
        predictions = model._trained_model.predict(test_features)
        predictions = np.argmax(predictions, axis=1)
        true_labels = test_labels
    
    # 计算评估指标
    accuracy = accuracy_score(true_labels, predictions)
    precision, recall, f1, support = precision_recall_fscore_support(
        true_labels, predictions, average=None, labels=range(10)
    )
    
    # 计算平均指标
    avg_precision = np.mean(precision)
    avg_recall = np.mean(recall)
    avg_f1 = np.mean(f1)
    
    # 生成分类报告
    report = classification_report(true_labels, predictions, 
                                 target_names=genre_names, 
                                 output_dict=True)
    
    # 生成混淆矩阵
    cm = confusion_matrix(true_labels, predictions)
    
    # 保存详细结果
    with open(f'{save_dir}/{model_name}_evaluation.txt', 'w', encoding='utf-8') as f:
        f.write(f"{model_name} 模型评估结果\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"总体准确率: {accuracy:.4f}\n")
        f.write(f"平均精确率: {avg_precision:.4f}\n")
        f.write(f"平均召回率: {avg_recall:.4f}\n")
        f.write(f"平均F1分数: {avg_f1:.4f}\n\n")
        
        f.write("各类别详细指标:\n")
        f.write("-" * 30 + "\n")
        for i, genre in enumerate(genre_names):
            f.write(f"{genre:>10}: 精确率={precision[i]:.4f}, "
                   f"召回率={recall[i]:.4f}, F1={f1[i]:.4f}, "
                   f"支持数={support[i]}\n")
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'support': support,
        'confusion_matrix': cm,
        'predictions': predictions,
        'true_labels': true_labels,
        'report': report
    }


def create_confusion_matrix_plot(cm, model_name, save_dir="results"):
    """创建混淆矩阵可视化"""
    genre_names = ['blues', 'classical', 'country', 'disco', 'hiphop', 
                   'jazz', 'metal', 'pop', 'reggae', 'rock']
    
    plt.figure(figsize=(10, 8))
    
    # 计算百分比
    cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100
    
    # 创建热力图
    sns.heatmap(cm_percent, annot=True, fmt='.1f', cmap='Blues',
                xticklabels=genre_names, yticklabels=genre_names,
                cbar_kws={'label': '百分比 (%)'})
    
    plt.title(f'{model_name} 混淆矩阵 (百分比)', fontsize=16, fontweight='bold')
    plt.xlabel('预测标签', fontsize=12)
    plt.ylabel('真实标签', fontsize=12)
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig(f'{save_dir}/{model_name}_confusion_matrix.png', dpi=300, bbox_inches='tight')
    plt.close()


def create_performance_comparison(results_dict, save_dir="results"):
    """创建模型性能对比图"""
    models = list(results_dict.keys())
    metrics = ['accuracy', 'precision', 'recall', 'f1']
    
    # 准备数据
    data = {
        'accuracy': [results_dict[model]['accuracy'] for model in models],
        'precision': [np.mean(results_dict[model]['precision']) for model in models],
        'recall': [np.mean(results_dict[model]['recall']) for model in models],
        'f1': [np.mean(results_dict[model]['f1']) for model in models]
    }
    
    # 创建对比图
    x = np.arange(len(models))
    width = 0.2
    
    fig, ax = plt.subplots(figsize=(12, 6))
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    metric_names = ['准确率', '精确率', '召回率', 'F1分数']
    
    for i, (metric, name, color) in enumerate(zip(metrics, metric_names, colors)):
        ax.bar(x + i * width, data[metric], width, label=name, color=color, alpha=0.8)
    
    ax.set_xlabel('模型', fontsize=12)
    ax.set_ylabel('分数', fontsize=12)
    ax.set_title('模型性能对比', fontsize=16, fontweight='bold')
    ax.set_xticks(x + width * 1.5)
    ax.set_xticklabels(models)
    ax.legend()
    ax.grid(axis='y', alpha=0.3)
    ax.set_ylim(0, 1)
    
    # 添加数值标签
    for i, (metric, color) in enumerate(zip(metrics, colors)):
        for j, value in enumerate(data[metric]):
            ax.text(j + i * width, value + 0.01, f'{value:.3f}', 
                   ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/model_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()


def create_genre_performance_analysis(results_dict, save_dir="results"):
    """创建各流派性能分析"""
    genre_names = ['blues', 'classical', 'country', 'disco', 'hiphop', 
                   'jazz', 'metal', 'pop', 'reggae', 'rock']
    
    models = list(results_dict.keys())
    
    # 为每个指标创建图表
    metrics = ['precision', 'recall', 'f1']
    metric_names = ['精确率', '召回率', 'F1分数']
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    for idx, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
        ax = axes[idx]
        
        # 准备数据
        data = []
        for model in models:
            data.append(results_dict[model][metric])
        
        data = np.array(data).T  # 转置，使得每行是一个流派
        
        # 创建热力图
        im = ax.imshow(data, cmap='RdYlBu_r', aspect='auto', vmin=0, vmax=1)
        
        # 设置标签
        ax.set_xticks(range(len(models)))
        ax.set_xticklabels(models)
        ax.set_yticks(range(len(genre_names)))
        ax.set_yticklabels(genre_names)
        
        # 添加数值标签
        for i in range(len(genre_names)):
            for j in range(len(models)):
                text = ax.text(j, i, f'{data[i, j]:.3f}',
                             ha="center", va="center", color="black", fontsize=9)
        
        ax.set_title(f'{metric_name}', fontsize=12, fontweight='bold')
        ax.set_xlabel('模型')
        if idx == 0:
            ax.set_ylabel('音乐流派')
    
    # 添加颜色条
    fig.colorbar(im, ax=axes, shrink=0.6, aspect=30)
    
    plt.suptitle('各音乐流派在不同模型上的性能表现', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(f'{save_dir}/genre_performance_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()


def save_comprehensive_report(results_dict, save_dir="results"):
    """保存综合评估报告"""
    with open(f'{save_dir}/comprehensive_evaluation_report.txt', 'w', encoding='utf-8') as f:
        f.write("音乐流派分类模型综合评估报告\n")
        f.write("=" * 60 + "\n\n")
        
        # 模型对比总结
        f.write("1. 模型性能总结\n")
        f.write("-" * 30 + "\n")
        for model_name, results in results_dict.items():
            f.write(f"\n{model_name}:\n")
            f.write(f"  准确率: {results['accuracy']:.4f}\n")
            f.write(f"  平均精确率: {np.mean(results['precision']):.4f}\n")
            f.write(f"  平均召回率: {np.mean(results['recall']):.4f}\n")
            f.write(f"  平均F1分数: {np.mean(results['f1']):.4f}\n")
        
        # 最佳模型
        best_model = max(results_dict.keys(), 
                        key=lambda x: results_dict[x]['accuracy'])
        f.write(f"\n2. 最佳模型: {best_model}\n")
        f.write(f"   准确率: {results_dict[best_model]['accuracy']:.4f}\n")
        
        # 各流派表现分析
        f.write(f"\n3. 各流派分类难度分析 (基于{best_model})\n")
        f.write("-" * 40 + "\n")
        genre_names = ['blues', 'classical', 'country', 'disco', 'hiphop', 
                       'jazz', 'metal', 'pop', 'reggae', 'rock']
        
        best_results = results_dict[best_model]
        genre_f1 = list(zip(genre_names, best_results['f1']))
        genre_f1.sort(key=lambda x: x[1], reverse=True)
        
        for i, (genre, f1_score) in enumerate(genre_f1):
            f.write(f"  {i+1:2d}. {genre:>10}: F1={f1_score:.4f}\n")


def main():
    """主函数"""
    print("开始模型评估...")
    
    # 加载数据和模型
    train_ds, test_ds, model_trainables, config = load_data_and_models()
    print(f"数据加载完成，开始训练和评估模型...")
    
    # 创建结果目录
    save_dir = "results"
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # 训练和评估所有模型
    results_dict = {}
    
    for i, model in enumerate(model_trainables):
        model_name = type(model).__name__
        print(f"\n正在处理模型 {i+1}/{len(model_trainables)}: {model_name}")
        
        # 调优和训练
        print("  - 超参数调优...")
        model.tune()
        print("  - 训练模型...")
        model.train()
        
        # 评估
        print("  - 评估性能...")
        results = evaluate_model_performance(model, test_ds, model_name, save_dir)
        results_dict[model_name] = results
        
        # 创建混淆矩阵
        create_confusion_matrix_plot(results['confusion_matrix'], model_name, save_dir)
        
        print(f"  - {model_name} 准确率: {results['accuracy']:.4f}")
    
    # 创建对比分析
    print("\n生成对比分析图表...")
    create_performance_comparison(results_dict, save_dir)
    create_genre_performance_analysis(results_dict, save_dir)
    
    # 保存综合报告
    save_comprehensive_report(results_dict, save_dir)
    
    print(f"\n所有评估结果已保存到 {save_dir} 目录")
    print("模型评估完成！")


if __name__ == "__main__":
    main()
