from typing import List
from typing import Tuple
import os
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import yaml

from music_genre_classifier import dataset
from music_genre_classifier import models


def create_data_analysis_plots(full_ds: np.ndarray, save_dir: str = "results"):
    """创建数据分析图表

    Parameters
    ----------
    full_ds : np.ndarray
        完整数据集
    save_dir : str
        保存目录
    """
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    # 获取特征和标签
    features, labels = dataset.split_features_and_labels(full_ds)

    # 音乐流派标签映射
    genre_names = ['blues', 'classical', 'country', 'disco', 'hiphop',
                   'jazz', 'metal', 'pop', 'reggae', 'rock']

    # 1. 数据集分布图
    plt.figure(figsize=(10, 6))
    unique_labels, counts = np.unique(labels, return_counts=True)
    genre_labels = [genre_names[int(label)] for label in unique_labels]

    plt.bar(genre_labels, counts, color='skyblue', alpha=0.7)
    plt.title('音乐流派数据集分布', fontsize=16, fontweight='bold')
    plt.xlabel('音乐流派', fontsize=12)
    plt.ylabel('样本数量', fontsize=12)
    plt.xticks(rotation=45)
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{save_dir}/genre_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 2. 特征相关性热力图
    feature_names = [
        'chroma_stft_mean', 'chroma_stft_var', 'rms_mean', 'rms_var',
        'spectral_centroid_mean', 'spectral_centroid_var', 'spectral_bandwidth_mean',
        'spectral_bandwidth_var', 'rolloff_mean', 'rolloff_var', 'zero_crossing_rate_mean',
        'zero_crossing_rate_var', 'harmony_mean', 'harmony_var', 'perceptr_mean',
        'perceptr_var', 'tempo'
    ]

    # 选择前17个特征进行相关性分析
    selected_features = features[:, :17]
    correlation_matrix = np.corrcoef(selected_features.T)

    plt.figure(figsize=(12, 10))
    sns.heatmap(correlation_matrix, annot=False, cmap='coolwarm', center=0,
                xticklabels=feature_names, yticklabels=feature_names)
    plt.title('音频特征相关性热力图', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(f'{save_dir}/feature_correlation.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 3. 主要特征分布箱线图
    key_features = ['tempo', 'spectral_centroid_mean', 'rms_mean', 'zero_crossing_rate_mean']
    key_indices = [16, 4, 2, 10]  # 对应特征在数组中的索引

    _, axes = plt.subplots(2, 2, figsize=(15, 10))
    axes = axes.ravel()

    for i, (feature_name, feature_idx) in enumerate(zip(key_features, key_indices)):
        data_for_plot = []
        labels_for_plot = []

        for genre_idx in range(10):
            genre_data = features[labels == genre_idx, feature_idx]
            data_for_plot.append(genre_data)
            labels_for_plot.append(genre_names[genre_idx])

        axes[i].boxplot(data_for_plot, labels=labels_for_plot)
        axes[i].set_title(f'{feature_name} 分布', fontsize=12, fontweight='bold')
        axes[i].tick_params(axis='x', rotation=45)
        axes[i].grid(alpha=0.3)

    plt.tight_layout()
    plt.savefig(f'{save_dir}/feature_distributions.png', dpi=300, bbox_inches='tight')
    plt.close()

    print(f"数据分析图表已保存到 {save_dir} 目录")


def save_model_results(model_trainables: List, results: List, save_dir: str = "results"):
    """保存模型结果

    Parameters
    ----------
    model_trainables : List
        训练好的模型列表
    results : List
        模型结果列表
    save_dir : str
        保存目录
    """
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # 保存结果到文件
    with open(f'{save_dir}/model_results.txt', 'w', encoding='utf-8') as f:
        f.write("音乐流派分类模型结果\n")
        f.write("=" * 50 + "\n\n")

        for model, result in zip(model_trainables, results):
            f.write(f"模型: {str(model)}\n")
            f.write(f"最佳超参数: {model._best_hyperparams}\n")
            f.write(f"测试结果: {result}\n")
            f.write("-" * 30 + "\n")

    print(f"模型结果已保存到 {save_dir}/model_results.txt")


if __name__ == "__main__":

    import argparse

    parser = argparse.ArgumentParser(prog="Music Genre Classifier")
    parser.add_argument("classifier_conf_path", help="path to yaml config for classifier")
    parser.add_argument(
        "--display_results",
        help="if should display results of training",
        action="store_true",
    )
    parser.add_argument(
        "--save_plots",
        help="if should save analysis plots",
        action="store_true",
    )

    args = parser.parse_args()
    with open(args.classifier_conf_path) as classifier_conf_file:
        classifier_conf = yaml.load(classifier_conf_file, Loader=yaml.Loader)

    print("开始加载GTZAN音乐数据集...")
    # create dataset
    full_ds: np.ndarray = dataset.create_gtzan_dataset(**classifier_conf["dataset"])
    print(f"数据集加载完成，共 {full_ds.shape[0]} 个样本，{full_ds.shape[1]-1} 个特征")

    # 创建数据分析图表
    if args.save_plots:
        print("正在生成数据分析图表...")
        create_data_analysis_plots(full_ds)

    print("正在划分训练集和测试集...")
    # create train, test, validation split
    train_ds, test_ds = dataset.split_dataset(full_ds)
    print(f"训练集: {train_ds.shape[0]} 样本，测试集: {test_ds.shape[0]} 样本")

    print("正在构建模型...")
    # create models from config
    model_trainables: List[models.ModelTrainable] = [
        models.build_from_config(model_conf, train_ds, test_ds)
        for model_conf in classifier_conf["models"]
    ]

    print("开始超参数调优...")
    # find hyperparameters (TODO: parallelize this later)
    for i, model in enumerate(model_trainables):
        print(f"正在调优模型 {i+1}/{len(model_trainables)}: {type(model).__name__}")
        model.tune()

    print("开始训练模型...")
    # train models (TODO: parallelize this later)
    for i, model in enumerate(model_trainables):
        print(f"正在训练模型 {i+1}/{len(model_trainables)}: {type(model).__name__}")
        model.train()

    print("开始评估模型...")
    # evaluate models (TODO: parallelize this later)
    results: List[Tuple[float, float]] = []
    for i, model in enumerate(model_trainables):
        print(f"正在评估模型 {i+1}/{len(model_trainables)}: {type(model).__name__}")
        results.append(model.test())

    # 保存结果
    save_model_results(model_trainables, results)

    # display results
    if args.display_results:
        print("\n" + "="*60)
        print("模型训练和评估结果:")
        print("="*60)
        for model, result in zip(model_trainables, results):
            print(f"\n模型: {str(model)}")
            print(f"最佳超参数: {model._best_hyperparams}")
            print(f"测试结果: {result}")

    print("\n实验完成！")   # type: ignore
