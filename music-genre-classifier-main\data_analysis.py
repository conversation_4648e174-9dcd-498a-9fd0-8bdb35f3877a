#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音乐流派分类数据分析脚本
用于生成论文所需的数据分析图表和统计信息
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import yaml

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 导入项目模块
import sys
sys.path.append('music_genre_classifier')
from music_genre_classifier import dataset


def load_data():
    """加载GTZAN数据集"""
    # 读取配置文件
    with open('configs/default.yaml', 'r') as f:
        config = yaml.load(f, Loader=yaml.Loader)
    
    # 加载数据集
    full_ds = dataset.create_gtzan_dataset(**config["dataset"])
    features, labels = dataset.split_features_and_labels(full_ds)
    
    return features, labels, config


def create_basic_statistics(features, labels, save_dir="results"):
    """创建基本统计信息"""
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # 音乐流派名称
    genre_names = ['blues', 'classical', 'country', 'disco', 'hiphop', 
                   'jazz', 'metal', 'pop', 'reggae', 'rock']
    
    # 基本统计信息
    stats = {
        '总样本数': len(features),
        '特征数': features.shape[1],
        '类别数': len(np.unique(labels)),
        '每类样本数': len(features) // len(np.unique(labels))
    }
    
    # 保存统计信息
    with open(f'{save_dir}/basic_statistics.txt', 'w', encoding='utf-8') as f:
        f.write("GTZAN音乐数据集基本统计信息\n")
        f.write("=" * 40 + "\n")
        for key, value in stats.items():
            f.write(f"{key}: {value}\n")
        
        f.write("\n音乐流派分布:\n")
        f.write("-" * 20 + "\n")
        unique_labels, counts = np.unique(labels, return_counts=True)
        for label, count in zip(unique_labels, counts):
            f.write(f"{genre_names[int(label)]}: {count} 样本\n")
    
    print(f"基本统计信息已保存到 {save_dir}/basic_statistics.txt")
    return stats


def create_feature_analysis(features, labels, save_dir="results"):
    """创建特征分析图表"""
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    genre_names = ['blues', 'classical', 'country', 'disco', 'hiphop', 
                   'jazz', 'metal', 'pop', 'reggae', 'rock']
    
    # 1. 数据集分布柱状图
    plt.figure(figsize=(12, 6))
    unique_labels, counts = np.unique(labels, return_counts=True)
    genre_labels = [genre_names[int(label)] for label in unique_labels]
    
    bars = plt.bar(genre_labels, counts, color='lightblue', alpha=0.8, edgecolor='navy')
    plt.title('GTZAN音乐数据集流派分布', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('音乐流派', fontsize=12)
    plt.ylabel('样本数量', fontsize=12)
    plt.xticks(rotation=45, ha='right')
    plt.grid(axis='y', alpha=0.3)
    
    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{int(height)}', ha='center', va='bottom', fontsize=10)
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/genre_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 主要音频特征分析
    feature_names = [
        'chroma_stft_mean', 'chroma_stft_var', 'rms_mean', 'rms_var',
        'spectral_centroid_mean', 'spectral_centroid_var', 'spectral_bandwidth_mean',
        'spectral_bandwidth_var', 'rolloff_mean', 'rolloff_var', 'zero_crossing_rate_mean',
        'zero_crossing_rate_var', 'harmony_mean', 'harmony_var', 'perceptr_mean',
        'perceptr_var', 'tempo'
    ]
    
    # 选择关键特征进行可视化
    key_features = ['tempo', 'spectral_centroid_mean', 'rms_mean', 'zero_crossing_rate_mean']
    key_indices = [16, 4, 2, 10]
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.ravel()
    
    for i, (feature_name, feature_idx) in enumerate(zip(key_features, key_indices)):
        data_for_plot = []
        labels_for_plot = []
        
        for genre_idx in range(10):
            genre_data = features[labels == genre_idx, feature_idx]
            data_for_plot.append(genre_data)
            labels_for_plot.append(genre_names[genre_idx])
        
        bp = axes[i].boxplot(data_for_plot, labels=labels_for_plot, patch_artist=True)
        
        # 设置颜色
        colors = plt.cm.Set3(np.linspace(0, 1, 10))
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        axes[i].set_title(f'{feature_name.replace("_", " ").title()} 分布', 
                         fontsize=12, fontweight='bold')
        axes[i].tick_params(axis='x', rotation=45)
        axes[i].grid(alpha=0.3)
    
    plt.suptitle('主要音频特征在不同流派中的分布', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(f'{save_dir}/feature_distributions.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"特征分析图表已保存到 {save_dir} 目录")


def create_correlation_analysis(features, save_dir="results"):
    """创建特征相关性分析"""
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # 选择前20个特征进行相关性分析
    selected_features = features[:, :20]
    correlation_matrix = np.corrcoef(selected_features.T)
    
    # 特征名称（简化版）
    feature_names = [
        'chroma_mean', 'chroma_var', 'rms_mean', 'rms_var',
        'centroid_mean', 'centroid_var', 'bandwidth_mean', 'bandwidth_var',
        'rolloff_mean', 'rolloff_var', 'zcr_mean', 'zcr_var',
        'harmony_mean', 'harmony_var', 'perceptr_mean', 'perceptr_var',
        'tempo', 'mfcc1_mean', 'mfcc1_var', 'mfcc2_mean'
    ]
    
    plt.figure(figsize=(14, 12))
    mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
    
    sns.heatmap(correlation_matrix, mask=mask, annot=False, cmap='RdBu_r', center=0,
                square=True, xticklabels=feature_names, yticklabels=feature_names,
                cbar_kws={"shrink": .8})
    
    plt.title('音频特征相关性热力图', fontsize=16, fontweight='bold', pad=20)
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig(f'{save_dir}/feature_correlation.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"相关性分析图表已保存到 {save_dir}/feature_correlation.png")


def create_dimensionality_reduction(features, labels, save_dir="results"):
    """创建降维可视化"""
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    genre_names = ['blues', 'classical', 'country', 'disco', 'hiphop', 
                   'jazz', 'metal', 'pop', 'reggae', 'rock']
    
    # PCA降维
    pca = PCA(n_components=2)
    features_pca = pca.fit_transform(features)
    
    # t-SNE降维（使用较小的样本以加快速度）
    sample_size = min(1000, len(features))
    indices = np.random.choice(len(features), sample_size, replace=False)
    features_sample = features[indices]
    labels_sample = labels[indices]
    
    tsne = TSNE(n_components=2, random_state=42, perplexity=30)
    features_tsne = tsne.fit_transform(features_sample)
    
    # 创建子图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
    
    # PCA可视化
    colors = plt.cm.tab10(np.linspace(0, 1, 10))
    for i in range(10):
        mask = labels == i
        ax1.scatter(features_pca[mask, 0], features_pca[mask, 1], 
                   c=[colors[i]], label=genre_names[i], alpha=0.6, s=20)
    
    ax1.set_title('PCA降维可视化', fontsize=14, fontweight='bold')
    ax1.set_xlabel(f'PC1 (解释方差: {pca.explained_variance_ratio_[0]:.2%})')
    ax1.set_ylabel(f'PC2 (解释方差: {pca.explained_variance_ratio_[1]:.2%})')
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax1.grid(alpha=0.3)
    
    # t-SNE可视化
    for i in range(10):
        mask = labels_sample == i
        ax2.scatter(features_tsne[mask, 0], features_tsne[mask, 1], 
                   c=[colors[i]], label=genre_names[i], alpha=0.6, s=20)
    
    ax2.set_title('t-SNE降维可视化', fontsize=14, fontweight='bold')
    ax2.set_xlabel('t-SNE 1')
    ax2.set_ylabel('t-SNE 2')
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.grid(alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/dimensionality_reduction.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 保存PCA解释方差信息
    with open(f'{save_dir}/pca_analysis.txt', 'w', encoding='utf-8') as f:
        f.write("PCA主成分分析结果\n")
        f.write("=" * 30 + "\n")
        f.write(f"前两个主成分解释的总方差: {sum(pca.explained_variance_ratio_[:2]):.2%}\n")
        f.write(f"PC1解释方差: {pca.explained_variance_ratio_[0]:.2%}\n")
        f.write(f"PC2解释方差: {pca.explained_variance_ratio_[1]:.2%}\n")
    
    print(f"降维可视化已保存到 {save_dir}/dimensionality_reduction.png")


def main():
    """主函数"""
    print("开始数据分析...")
    
    # 加载数据
    features, labels, config = load_data()
    print(f"数据加载完成: {features.shape[0]} 样本, {features.shape[1]} 特征")
    
    # 创建结果目录
    save_dir = "results"
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # 执行各种分析
    print("1. 生成基本统计信息...")
    create_basic_statistics(features, labels, save_dir)
    
    print("2. 生成特征分析图表...")
    create_feature_analysis(features, labels, save_dir)
    
    print("3. 生成相关性分析...")
    create_correlation_analysis(features, save_dir)
    
    print("4. 生成降维可视化...")
    create_dimensionality_reduction(features, labels, save_dir)
    
    print(f"\n所有分析结果已保存到 {save_dir} 目录")
    print("数据分析完成！")


if __name__ == "__main__":
    main()
