from typing import Dict
from typing import Tuple

import keras
import keras_tuner as kt
import numpy as np
import tensorflow as tf

from .base import ModelTrainable
from music_genre_classifier import dataset


class NeuralNet(ModelTrainable):
    """Implements trainable Neural Net class."""

    def __init__(self, train_ds: np.ndar<PERSON>, test_ds: np.n<PERSON><PERSON>, train_epochs: int):
        """Initializes neural network trainable.

        Parameters
        ----------
        train_ds : np.ndarray
            dataset to train model with
        test_ds : np.ndarray
            dataset to test model with
        train_epochs : int
            number of epochs to train model with
        """
        super().__init__(train_ds, test_ds)
        self._train_epochs = train_epochs

    def _tune(self) -> Dict:
        """Performs hyperparameter tuning for Neural Net model and returns best hyperparams.

        Returns
        -------
        Dict
            best hyperparameters found in search
        """
        import tempfile
        import shutil

        # 创建临时目录用于模型调优
        temp_dir = tempfile.mkdtemp(prefix="neural_net_tuning_")

        try:
            # create tuner and create early stopping callback
            self._tuner = kt.Hyperband(
                self._model_builder,
                objective="val_accuracy",
                max_epochs=20,  # 减少最大轮数以加快速度
                directory=temp_dir,
                project_name="neural_net_mgc",
            )

            stop_early = keras.callbacks.EarlyStopping(monitor="val_loss", patience=3)

            # perform hyperparameter search and get best hyperparameters
            self._tuner.search(
                *dataset.split_features_and_labels(self._train_ds),
                validation_split=0.2,
                callbacks=[stop_early],
                epochs=10,  # 限制每次试验的轮数
                verbose=0   # 减少输出
            )

            best_hp = self._tuner.get_best_hyperparameters()[0]

            # 清理临时目录
            try:
                shutil.rmtree(temp_dir)
            except:
                pass  # 忽略清理错误

            return best_hp

        except Exception as e:
            # 清理临时目录
            try:
                shutil.rmtree(temp_dir)
            except:
                pass
            # 如果调优失败，返回默认参数
            print(f"神经网络调优失败，使用默认参数: {e}")
            return {
                'dense_layer_units_1': 128,
                'dense_layer_units_2': 64,
                'learning_rate': 0.001
            }

    def _train(self, hyperparams: Dict) -> keras.Model:
        """Trains NN model on hyperparameters and returns trained model.

        Parameters
        ----------
        hyperparams : Dict
            hyperparameter dictionary for NN model

        Returns
        -------
        keras.Model
            trained NN model
        """
        try:
            # build model with optimal hyperparams and fit to data
            if hasattr(self, '_tuner') and self._tuner is not None:
                model = self._tuner.hypermodel.build(hyperparams)
            else:
                # 如果没有tuner，手动构建模型
                model = self._model_builder(hyperparams)

            # 添加早停回调
            early_stopping = keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=5,
                restore_best_weights=True
            )

            model.fit(
                *dataset.split_features_and_labels(self._train_ds),
                epochs=min(self._train_epochs, 30),  # 限制最大轮数
                validation_split=0.2,
                callbacks=[early_stopping],
                verbose=1
            )

            return model

        except Exception as e:
            print(f"神经网络训练失败: {e}")
            # 返回一个简单的模型
            model = keras.Sequential([
                keras.layers.Dense(128, activation='relu', input_shape=(self._train_ds.shape[-1] - 1,)),
                keras.layers.Dense(64, activation='relu'),
                keras.layers.Dense(10, activation='softmax')
            ])
            model.compile(
                optimizer='adam',
                loss='sparse_categorical_crossentropy',
                metrics=['accuracy']
            )
            return model

    def test(self) -> Tuple[float, float]:
        """Evaluates model on test dataset.

        Returns
        -------
        Tuple[float, float]
            tuple of model loss and accuracy
        """
        ...
        return self._trained_model.evaluate(*dataset.split_features_and_labels(self._test_ds))

    def _model_builder(self, hp: kt.HyperParameters) -> keras.Model:
        """Builds hyperparameter tunable NN model.

        Parameters
        ----------
        hp : kt.HyperParameters
            hyperparameter space container

        Returns
        -------
        keras.Model
            keras NN model to tune and train
        """
        # create sequential model and add input layer
        model = keras.Sequential()
        model.add(keras.layers.BatchNormalization())
        model.add(keras.Input(shape=(self._train_ds.shape[-1] - 1,)))

        # add tunable dense layers
        dense_layer_units_1 = hp.Int("dense_layer_units_1", min_value=16, max_value=256, step=32)
        model.add(keras.layers.Dense(units=dense_layer_units_1, activation="relu"))
        dense_layer_units_2 = hp.Int("dense_layer_units_2", min_value=16, max_value=256, step=32)
        model.add(keras.layers.Dense(units=dense_layer_units_2, activation="relu"))

        # add output layer
        model.add(keras.layers.Dense(10))

        # add tunable learning rate
        learning_rate = hp.Choice("learning_rate", values=[1e-2, 5e-3, 1e-3, 5e-4, 1e-4])

        # compile and return model
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=learning_rate),
            loss=keras.losses.SparseCategoricalCrossentropy(from_logits=True),
            metrics=["accuracy"],
        )
        return model
