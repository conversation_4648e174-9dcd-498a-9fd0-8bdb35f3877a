
# 音乐流派分类实验总结报告

**实验时间**: 2025-06-02 10:25:19

## 实验概述

本次实验基于GTZAN音乐数据集，使用机器学习方法进行音乐流派分类研究。

## 生成的文件

### 数据分析结果
- `results/basic_statistics.txt` - 数据集基本统计信息
- `results/genre_distribution.png` - 音乐流派分布图
- `results/feature_distributions.png` - 主要特征分布图
- `results/feature_correlation.png` - 特征相关性热力图
- `results/dimensionality_reduction.png` - 降维可视化图
- `results/pca_analysis.txt` - PCA分析结果

### 模型评估结果
- `results/model_results.txt` - 模型训练结果
- `results/*_evaluation.txt` - 各模型详细评估报告
- `results/*_confusion_matrix.png` - 混淆矩阵图
- `results/model_comparison.png` - 模型性能对比图
- `results/genre_performance_analysis.png` - 各流派性能分析图
- `results/comprehensive_evaluation_report.txt` - 综合评估报告

## 论文文档
- `基于GTZAN数据集的音乐流派分类研究.md` - 完整论文

## 使用说明

1. 所有图表文件可直接插入到论文中对应位置
2. 评估报告包含详细的数值结果，可用于填充论文中的表格
3. 论文模板已按照作业要求格式化，只需填入个人信息和实验结果

## 注意事项

- 图片插入位置已在论文中标注
- 实验结果数值需要根据实际运行结果填入
- 建议根据实际结果调整论文中的分析和结论

---
实验完成时间: 2025-06-02 10:25:19
